<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TikTok Live Overlay</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      background-color: transparent;
      height: 100vh;
      width: 100vw;
      position: relative;
    }

    #actionContainer {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 10;
      pointer-events: none;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .action-item {
      max-width: 80%;
      max-height: 80%;
      opacity: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      /* إزالة الانتقال التلقائي لمنع الحركة غير المرغوبة */
    }

    .action-item.active {
      opacity: 1;
    }



    .action-item img, .action-item video {
      max-width: 100%;
      max-height: 100%;
      display: block;
    }

    /* تم إزالة المواضع المختلفة واستخدام الوسط فقط في .action-item */

    .audio-icon {
      width: 60px;
      height: 60px;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }





    /* تنسيق عرض اسم المستخدم المنضم */
    .username-display {
      font-size: 1.5em;
      font-weight: bold;
      margin-bottom: 10px;
      text-shadow: 0 0 10px rgba(255, 59, 92, 0.7);
      color: #ffffff;
    }

    /* تنسيق النص المخصص */
    .custom-text {
      font-size: 1.2em;
      margin-top: 10px;
      text-align: center;
      color: #ffffff;
    }

    /* تنسيق حاوية الوسائط والنص */
    .media-text-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    /* أنماط عرض صورة المستخدم */
    .user-display-item {
      pointer-events: none;
      z-index: 10001;
    }

    .user-container {
      animation: userDisplayFadeIn 0.3s ease-out;
    }

    @keyframes userDisplayFadeIn {
      from {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
      }
      to {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
    }

    /* مواقع العرض - 9 مواقع مختلفة */
    .user-display-item.position-top-left {
      top: 20px;
      left: 20px;
    }

    .user-display-item.position-top-center {
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
    }

    .user-display-item.position-top-right {
      top: 20px;
      right: 20px;
    }

    .user-display-item.position-middle-left {
      top: 50%;
      left: 20px;
      transform: translateY(-50%);
    }

    .user-display-item.position-center {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .user-display-item.position-middle-right {
      top: 50%;
      right: 20px;
      transform: translateY(-50%);
    }

    .user-display-item.position-bottom-left {
      bottom: 20px;
      left: 20px;
    }

    .user-display-item.position-bottom-center {
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
    }

    .user-display-item.position-bottom-right {
      bottom: 20px;
      right: 20px;
    }
  </style>
</head>
<body>
  <div id="actionContainer"></div>

  <script src="/socket.io/socket.io.js"></script>
  <script>
    // اتصال Socket.IO
    const socket = io({
      reconnectionAttempts: 5,
      timeout: 20000,
      forceNew: true,
      transports: ['websocket', 'polling']
    });

    // العناصر في DOM
    const actionContainer = document.getElementById('actionContainer');

    // قائمة للإجراءات في الانتظار
    let actionQueue = [];
    let availableActions = [];

    // الانضمام إلى غرفة العرض المناسبة
    let overlayId = 'default';
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('id')) {
      overlayId = urlParams.get('id');
    }
    socket.emit('joinOverlayRoom', { overlayId: overlayId });
    console.log(`انضمام إلى غرفة شاشة العرض: ${overlayId}`);

    // إعلان متغير giftMappings
    let giftMappings = [];

    // استقبال تحديثات التعيينات
    socket.on('giftMappingsUpdated', (data) => {
      console.log('تم استلام تحديثات التعيينات:', data);
      if (data && data.mappings && Array.isArray(data.mappings)) {
        giftMappings = data.mappings;
      } else if (Array.isArray(data)) {
        giftMappings = data;
      } else {
        console.warn('تم استلام بيانات ربط هدايا بتنسيق غير متوقع:', data);
      }
      console.log(`تحديث التعيينات: عدد التعيينات المستلمة = ${giftMappings.length}`);
    });

    // استقبال تعيينات الأحداث (نفس المعالج)
    socket.on('giftMappings', (data) => {
      console.log('تم استلام تعيينات الأحداث:', data);
      if (data && data.mappings && Array.isArray(data.mappings)) {
        giftMappings = data.mappings;
      } else if (Array.isArray(data)) {
        giftMappings = data;
      } else {
        console.warn('تم استلام بيانات ربط هدايا بتنسيق غير متوقع:', data);
      }
      console.log(`تحديث التعيينات: عدد التعيينات المستلمة = ${giftMappings.length}`);
    });

    // طلب التعيينات من الخادم عند الاتصال
    socket.on('connect', () => {
      console.log('تم الاتصال بالخادم، جاري طلب التعيينات...');
      socket.emit('getGiftMappings');
      socket.emit('getDisplaySettings');
    });

    socket.on('connect_error', (err) => {
      console.error('Connection error:', err);
    });

    socket.on('disconnect', (reason) => {
      console.warn('Disconnected:', reason);
    });

    // معالجة أحداث الهدايا (للإحصائيات فقط - التنفيذ يتم عبر eventAction)
    socket.on('gift', (data) => {
      console.log('Gift received:', data);
      // ملاحظة: لا نستدعي findAndQueueAction هنا لأن الخادم يتولى التنفيذ عبر eventAction
    });

    // معالجة أحداث الهدايا المحاكاة (للإحصائيات فقط - التنفيذ يتم عبر eventAction)
    socket.on('simulatedGift', (data) => {
      console.log('🧪 Simulated Gift received:', data);
      // ملاحظة: لا نستدعي findAndQueueAction هنا لأن الخادم يتولى التنفيذ عبر eventAction
    });

    // معالجة أحداث التعليقات المحاكاة
    socket.on('simulatedComment', (data) => {
      console.log('🧪 Simulated Comment received:', data);
    });

    // معالجة أحداث الإعجابات المحاكاة
    socket.on('simulatedLike', (data) => {
      console.log('🧪 Simulated Like received:', data);
    });

    // معالجة أحداث المتابعة المحاكاة
    socket.on('simulatedFollow', (data) => {
      console.log('🧪 Simulated Follow received:', data);
    });

    // معالجة أحداث المشاركة المحاكاة
    socket.on('simulatedShare', (data) => {
      console.log('🧪 Simulated Share received:', data);
    });

    // معالجة أحداث الانضمام المحاكاة
    socket.on('simulatedJoin', (data) => {
      console.log('🧪 Simulated Join received:', data);
    });

    // استقبال أحداث اختبار الهدايا
    socket.on('testGift', (data) => {
      console.log('Test gift received:', data);

      // في حالة اختبار عرض الإجراء، نتعامل معه بشكل مباشر
      if (data.testMode === true) {
        console.log('معالجة إجراء اختبار مباشرة:', data);

        // تصحيح إنشاء كائن تعيين مؤقت للاختبار
        const mediaPath = data.mediaFilePath || data.filePath;
        console.log('مسار الوسائط المستخدم في الاختبار:', mediaPath);

        // تحديد مدة العرض (إذا كانت صفر أو غير محددة، استخدم 5 ثوانٍ كقيمة افتراضية)
        const duration = parseInt(data.duration) || 5;
        console.log('مدة العرض (بالثواني):', duration);

        const testMapping = {
          eventType: data.eventType || 'gift',
          giftId: data.giftId || 'test',
          giftName: data.giftName || 'اختبار',
          nickname: data.nickname || 'مستخدم',
          actions: data.actions || [data.type || 'image'],
          duration: duration,
          // ضمان أن يتم استخدام مسار ملف واحد فقط
          mediaFilePath: mediaPath,
          filePath: mediaPath, // تخزين في كلا الحقلين للتأكد
          soundFilePath: data.soundFile,
          customText: data.customText || '',
          testMode: true
        };

        console.log('بيانات اختبار النص المخصص في testGift:', {
          hasTextAction: testMapping.actions.includes('text'),
          customText: testMapping.customText,
          actions: testMapping.actions
        });

        console.log('تم إنشاء تعيين اختبار مؤقت:', testMapping);

        // إضافة الإجراء مباشرة للعرض بدلاً من قائمة الانتظار
        setTimeout(() => {
          showAction(testMapping);
        }, 100); // تأخير قصير لضمان جاهزية الملفات
      } else {
        // تصرف عادي للبحث عن الإجراء المقابل
        findAndQueueAction(data.giftName, data.giftId);
      }
    });

    // استقبال أحداث الإجراءات من الأنواع المختلفة
    socket.on('eventAction', (data) => {
      console.log('Event action received:', data);

      // إنشاء كائن تعيين مؤقت للإجراء
      const actionMapping = {
        eventType: data.eventType || 'gift',
        giftId: data.giftId || 'event',
        giftName: data.nickname || 'حدث',
        nickname: data.nickname || '',
        uniqueId: data.uniqueId || '',
        profilePictureUrl: data.profilePictureUrl || null,
        actions: data.actions || [data.type || 'alert'],
        duration: parseInt(data.duration) || 5,
        mediaFilePath: data.mediaFilePath,
        soundFilePath: data.soundFilePath,
        alertMessage: data.message || 'تنبيه',
        customText: data.customText || '',
        userDisplaySettings: data.userDisplaySettings || null
      };

      console.log('استلام بيانات الإجراء مع النص المخصص:', {
        hasTextAction: actionMapping.actions.includes('text'),
        customText: actionMapping.customText,
        actions: actionMapping.actions
      });

      console.log('تم إنشاء تعيين إجراء:', actionMapping);

      // إضافة الإجراء إلى قائمة الانتظار
      actionQueue.push(actionMapping);

      // بدء تشغيل قائمة الانتظار إذا لم تكن قيد التشغيل بالفعل
      if (actionQueue.length === 1) {
        processActionQueue();
      }
    });

    // البحث عن الإجراء المقابل لهدية معينة وإضافته إلى قائمة الانتظار
    function findAndQueueAction(giftName, giftId) {
      let mapping = null;
      // مطابقة giftId كنص
      if (giftId && giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
        mapping = giftMappings.find(m => String(m.giftId) === String(giftId));
        if (mapping) {
          console.log(`تم العثور على إجراء بواسطة معرف الهدية: ${giftId}`);
        }
      }
      // مطابقة اسم الهدية بشكل مرن
      if (!mapping && giftName && giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
        mapping = giftMappings.find(m =>
          m.giftName &&
          m.giftName.trim().toLowerCase() === giftName.trim().toLowerCase()
        );
        if (mapping) {
          console.log(`تم العثور على إجراء بواسطة اسم الهدية: ${giftName}`);
        }
      }
      // مطابقة عبر مكتبة الهدايا إذا لم يتم العثور
      if (!mapping && typeof availableGifts !== 'undefined' && Array.isArray(availableGifts)) {
        const giftObj = availableGifts.find(g =>
          String(g.id) === String(giftId) ||
          (g.name && g.name.trim().toLowerCase() === (giftName ? giftName.trim().toLowerCase() : ''))
        );
        if (giftObj) {
          mapping = giftMappings.find(m => String(m.giftId) === String(giftObj.id));
          if (mapping) {
            console.log('تم العثور على إجراء عبر مكتبة الهدايا:', mapping);
          }
        }
      }
      // البحث عن "أي هدية" إذا لم يتم العثور على مطابقة محددة
      if (!mapping && giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
        mapping = giftMappings.find(m => m.giftId === 'any');
        if (mapping) {
          console.log(`تم العثور على إجراء لـ "أي هدية"`);
        }
      }
      if (mapping) {
        console.log('تم العثور على إجراء:', mapping);
        // إضافة الإجراء إلى قائمة الانتظار
        actionQueue.push(mapping);
        // بدء تشغيل قائمة الانتظار إذا لم تكن قيد التشغيل بالفعل
        if (actionQueue.length === 1) {
          processActionQueue();
        }
      } else {
        console.log(`لم يتم العثور على إجراء للهدية: ${giftName} (ID: ${giftId})`);
      }
    }

    // معالجة قائمة انتظار الإجراءات
    function processActionQueue() {
      if (actionQueue.length === 0) {
        return;
      }

      const currentAction = actionQueue[0];
      showAction(currentAction);

      // إزالة الإجراء من القائمة بعد المدة المحددة
      setTimeout(() => {
        actionQueue.shift();

        // إخفاء الإجراء الحالي
        const actionElement = document.querySelector('.action-item.active');
        if (actionElement) {
          actionElement.classList.remove('active');

          // إزالة العنصر بعد انتهاء التأثير البصري
          setTimeout(() => {
            if (actionElement && actionElement.parentNode) {
              actionElement.remove();
            }

            // معالجة الإجراء التالي في القائمة
            if (actionQueue.length > 0) {
              processActionQueue();
            }
          }, 500);
        } else {
          // معالجة الإجراء التالي في القائمة
          if (actionQueue.length > 0) {
            processActionQueue();
          }
        }
      }, currentAction.duration * 1000 || 5000);
    }

    // متغيرات عامة للإعدادات
    let overlaySettings = {
      backgroundColor: '#000000',
      textColor: '#ffffff',
      accentColor: '#ff3b5c',
      fontFamily: "'Tajawal', sans-serif",
      fontSize: 16,
      opacity: 80
    };

    // استقبال تحديثات إعدادات الـ overlay
    socket.on('overlaySettingsUpdated', (data) => {
      console.log('تم استلام تحديثات إعدادات الـ overlay:', data);
      if (data && data.settings) {
        overlaySettings = data.settings;
        console.log('تم تحديث إعدادات الـ overlay:', overlaySettings);
      }
    });

    // طلب الإعدادات الحالية عند التحميل
    // تم دمج هذا مع معالج connect الأول



    // عرض الإجراء في الواجهة
    function showAction(mapping) {
      try {
        console.log('بدء عرض الإجراء:', mapping);
        const actions = mapping.actions || [mapping.action || 'alert'];
        let actionElement = null;

        // محاكاة ضغط مفتاح إذا كان موجودًا
        if (actions.includes('keypress') && mapping.keypressInfo) {
          simulateKeypress(mapping.keypressInfo);
        }

        // عرض صورة المستخدم إذا كان مطلوباً
        if (actions.includes('user-display')) {
          console.log('عرض صورة المستخدم:', mapping.nickname, mapping.uniqueId);
          showUserDisplay(mapping);
        }

        // عرض تنبيه بسيط إذا كان مطلوباً أو عرض نص مخصص فقط
        if (actions.includes('alert') || (actions.includes('text') && mapping.customText && !actions.includes('image') && !actions.includes('video') && !actions.includes('user-display'))) {
          console.log('عرض تنبيه أو نص مخصص فقط');
          actionElement = document.createElement('div');
          actionElement.className = 'action-item';

          // إذا كان نوع الحدث هو انضمام، نعرض اسم المستخدم المنضم
          if (mapping.eventType === 'join' && mapping.nickname) {
            const usernameElement = document.createElement('div');
            usernameElement.className = 'username-display';
            usernameElement.textContent = mapping.nickname;
            actionElement.appendChild(usernameElement);
          }

          // عرض النص المخصص إذا كان موجوداً
          if (actions.includes('text') && mapping.customText) {
            console.log('إضافة نص مخصص في وضع التنبيه:', mapping.customText);
            const customTextElement = document.createElement('div');
            customTextElement.className = 'custom-text';
            customTextElement.textContent = mapping.customText;
            customTextElement.style.fontSize = '1.3em';
            customTextElement.style.padding = '10px';
            customTextElement.style.margin = '10px 0';
            customTextElement.style.color = '#ffffff';
            customTextElement.style.fontWeight = 'bold';
            customTextElement.style.textShadow = '1px 1px 2px black';
            actionElement.appendChild(customTextElement);
          }
          // عرض رسالة التنبيه إذا كان مطلوباً وليس هناك نص مخصص
          else if (actions.includes('alert')) {
            const alertElement = document.createElement('div');
            alertElement.textContent = mapping.alertMessage || 'تنبيه بسيط';
            actionElement.appendChild(alertElement);
          }

          // تطبيق الإعدادات المخصصة
          applyCustomStyles(actionElement);

          actionContainer.appendChild(actionElement);

          // إعداد انتقال بسيط
          actionElement.style.transition = 'opacity 0.1s ease';

          setTimeout(() => {
            actionElement.classList.add('active');
          }, 100);
        } else {
          // صورة أو فيديو
          if (mapping.filePath || mapping.mediaFilePath) {
            actionElement = document.createElement('div');
            actionElement.className = 'action-item';

            // إنشاء حاوية للوسائط والنص
            const mediaTextContainer = document.createElement('div');
            mediaTextContainer.className = 'media-text-container';
            actionElement.appendChild(mediaTextContainer);

            let mediaPath = mapping.filePath || mapping.mediaFilePath;
            if (mediaPath && !mediaPath.startsWith('http') && !mediaPath.startsWith('/')) {
              mediaPath = '/' + mediaPath;
            }

            console.log('مسار الوسائط المستخدم:', mediaPath);

            // إذا كان نوع الحدث هو انضمام، نعرض اسم المستخدم المنضم
            if (mapping.eventType === 'join' && mapping.nickname) {
              const usernameElement = document.createElement('div');
              usernameElement.className = 'username-display';
              usernameElement.textContent = mapping.nickname;
              mediaTextContainer.appendChild(usernameElement);
            }

            if (mediaPath && (mediaPath.match(/\.(jpg|jpeg|png|gif|webp)$/i) || actions.includes('image'))) {
              console.log('إنشاء عنصر صورة مع المسار:', mediaPath);
              const img = document.createElement('img');
              img.src = mediaPath;
              img.alt = mapping.giftName || 'صورة';
              img.onerror = function() {
                console.error('خطأ في تحميل الصورة:', mediaPath);
                this.src = '/placeholder.png';
              };
              img.onload = function() {
                console.log('تم تحميل الصورة بنجاح:', mediaPath);
              };
              mediaTextContainer.appendChild(img);



            } else if (mediaPath && (mediaPath.match(/\.(mp4|webm|ogg|mov)$/i) || actions.includes('video'))) {
              console.log('إنشاء عنصر فيديو مع المسار:', mediaPath);
              const video = document.createElement('video');
              video.src = mediaPath;
              video.autoplay = true;
              video.loop = mapping.loop || false;
              video.muted = mapping.muted || false;
              video.controls = true; // إضافة عناصر التحكم للتشخيص
              video.onerror = function(e) {
                console.error('خطأ في تحميل الفيديو:', mediaPath, e);
              };
              video.onloadeddata = function() {
                console.log('تم تحميل الفيديو بنجاح:', mediaPath);
              };
              mediaTextContainer.appendChild(video);
            }

            // إضافة النص المخصص إذا كان موجوداً
            if (actions.includes('text') && mapping.customText) {
              console.log('إضافة نص مخصص:', mapping.customText);
              const customTextElement = document.createElement('div');
              customTextElement.className = 'custom-text';
              customTextElement.textContent = mapping.customText;
              customTextElement.style.marginTop = '10px';
              customTextElement.style.padding = '5px';
              customTextElement.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
              customTextElement.style.borderRadius = '5px';
              customTextElement.style.color = '#ffffff';
              customTextElement.style.fontWeight = 'bold';
              customTextElement.style.textShadow = '1px 1px 2px black';
              mediaTextContainer.appendChild(customTextElement);
            } else {
              console.log('لا يوجد نص مخصص أو لم يتم تحديد إجراء النص', {
                hasTextAction: actions.includes('text'),
                customText: mapping.customText
              });
            }

            // جعل خلفية العنصر شفافة تماماً
            actionElement.style.backgroundColor = 'transparent';
            actionElement.style.border = 'none';
            actionElement.style.boxShadow = 'none';
            actionElement.style.padding = '0';

            // Ya no es necesario añadir clase de posición, está en el CSS base
            actionContainer.appendChild(actionElement);

            // Primero hacer el elemento visible inmediatamente para evitar el "salto"
            actionElement.classList.add('active');


          }
        }

        // صوت
        if (actions.includes('sound') || mapping.soundFile || mapping.soundFilePath) {
          let soundPath = mapping.soundFile || mapping.soundFilePath;
          if (soundPath && !soundPath.startsWith('http') && !soundPath.startsWith('/')) {
            soundPath = '/' + soundPath;
          }

          // التحقق مما إذا كان الصوت يجب تشغيله على شاشة العرض
          // إذا كان خيار تشغيل الصوت على الخادم مفعلاً، لا نشغل الصوت هنا
          if (mapping.playSoundOnServer !== true) {
            console.log(`تشغيل الصوت على شاشة العرض: ${soundPath}`);

            const audio = document.createElement('audio');
            audio.src = soundPath;
            audio.autoplay = true;
            audio.style.display = 'none';

            // تطبيق مستوى الصوت من الإعدادات
            if (overlaySettings.sound && typeof overlaySettings.sound.volume === 'number') {
              audio.volume = overlaySettings.sound.volume / 100;
            }

            if (actionElement) {
              actionElement.appendChild(audio);
            } else {
              document.body.appendChild(audio);
            }

            audio.play().catch(err => console.error('فشل تشغيل الصوت:', err));
          } else {
            console.log(`تم تجاهل تشغيل الصوت على شاشة العرض لأنه سيتم تشغيله على الخادم: ${soundPath}`);
          }
        }

        // إزالة العنصر بعد انتهاء المدة
        if (actionElement) {
          let duration = parseInt(mapping.duration || 5);
          if (isNaN(duration) || duration < 1) duration = 5;

          setTimeout(() => {
            if (actionElement && actionElement.parentNode) {
              actionElement.classList.remove('active');

              setTimeout(() => {
                if (actionElement && actionElement.parentNode) {
                  actionElement.remove();
                }
              }, 500);
            }
          }, duration * 1000);
        }
      } catch (error) {
        console.error('خطأ في عرض الإجراء:', error);
      }
    }

    // تطبيق الإعدادات المخصصة على العنصر
    function applyCustomStyles(element) {
      if (!element) return;

      // تطبيق الخلفية مع الشفافية
      if (overlaySettings.backgroundColor) {
        // استخدام قيمة شفافية الخلفية المخصصة إذا كانت موجودة
        console.log('تطبيق شفافية الخلفية:', overlaySettings.backgroundOpacity);

        // التأكد من أن القيمة رقمية
        let bgOpacity = 80;
        if (typeof overlaySettings.backgroundOpacity === 'number') {
          bgOpacity = overlaySettings.backgroundOpacity;
        } else if (overlaySettings.backgroundOpacity !== undefined) {
          bgOpacity = parseInt(overlaySettings.backgroundOpacity, 10);
          if (isNaN(bgOpacity)) bgOpacity = 80;
        } else if (typeof overlaySettings.opacity === 'number') {
          bgOpacity = overlaySettings.opacity;
        } else if (overlaySettings.opacity !== undefined) {
          bgOpacity = parseInt(overlaySettings.opacity, 10);
          if (isNaN(bgOpacity)) bgOpacity = 80;
        }

        const opacity = bgOpacity / 100;
        const bgColor = hexToRgba(overlaySettings.backgroundColor, opacity);
        element.style.backgroundColor = bgColor;

        console.log('تم تطبيق لون الخلفية:', bgColor, 'مع شفافية:', bgOpacity);
      }

      // تطبيق لون النص
      if (overlaySettings.textColor) {
        element.style.color = overlaySettings.textColor;
      }

      // تطبيق نوع الخط
      if (overlaySettings.fontFamily) {
        element.style.fontFamily = overlaySettings.fontFamily;
      }

      // تطبيق حجم الخط
      if (overlaySettings.fontSize) {
        element.style.fontSize = `${overlaySettings.fontSize}px`;
      }

      // إضافة تأثيرات إضافية
      element.style.borderRadius = '10px';
      element.style.padding = '15px';
      element.style.boxShadow = `0 5px 15px rgba(0, 0, 0, 0.3)`;

      // إضافة حدود ملونة
      if (overlaySettings.accentColor) {
        element.style.border = `2px solid ${overlaySettings.accentColor}`;
      }
    }

    // تحويل لون HEX إلى RGBA
    function hexToRgba(hex, opacity) {
      if (!hex) return `rgba(0, 0, 0, ${opacity})`;

      hex = hex.replace('#', '');
      const r = parseInt(hex.substring(0, 2), 16);
      const g = parseInt(hex.substring(2, 4), 16);
      const b = parseInt(hex.substring(4, 6), 16);

      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    }

    socket.on('available-actions', (data) => {
      console.log('available-actions', data);
      if (Array.isArray(data)) {
        availableActions = data;
      } else {
        availableActions = [];
      }
    });

    // تم دمج هذا مع معالج giftMappingsUpdated

    function displayActions(data) {
      const { action, availableOverlays } = data;

      // تخزين رقم الـ overlay المحدد
      const overlayNumber = data.overlayNumber || 1;

      // التحقق إذا كان هذا الـ overlay هو المقصود
      if (overlayNumber != overlayId) {
        console.log(`هذا الإجراء مخصص للـ Overlay رقم ${overlayNumber}، بينما هذا هو رقم ${overlayId}`);
        return;
      }

      // البحث عن الإجراءات المناسبة
      if (Array.isArray(action) && Array.isArray(availableActions)) {
        const foundActions = action.map(actionId => {
          return availableActions.find(a => a.id === actionId);
        }).filter(Boolean);

        console.log('الإجراءات التي سيتم عرضها:', foundActions);

        foundActions.forEach(foundAction => {
          if (foundAction) {
            const actionElement = document.createElement('div');
            actionElement.className = 'action-item';

            // جعل العنصر شفافًا تمامًا (بدون خلفية أو إطار)
            actionElement.style.backgroundColor = 'transparent';
            actionElement.style.border = 'none';
            actionElement.style.boxShadow = 'none';
            actionElement.style.padding = '0';

            // Ya no es necesario añadir clase de posición, está en el CSS base

            if (foundAction.mediaType === 'image') {
              const img = document.createElement('img');
              img.src = foundAction.mediaUrl;
              actionElement.appendChild(img);
            } else if (foundAction.mediaType === 'video') {
              const video = document.createElement('video');
              video.src = foundAction.mediaUrl;
              video.autoplay = true;
              video.loop = foundAction.loop;
              video.muted = false;
              actionElement.appendChild(video);
            } else if (foundAction.mediaType === 'audio') {
              const audio = document.createElement('audio');
              audio.src = foundAction.mediaUrl;
              audio.autoplay = true;

              // إظهار أيقونة صوت لتمثيل الملف الصوتي
              const audioIcon = document.createElement('div');
              audioIcon.className = 'audio-icon';
              audioIcon.innerHTML = '<svg viewBox="0 0 24 24" width="64" height="64"><path fill="white" d="M3,9v6h4l5,5V4L7,9H3z M16.5,12c0-1.77-1.02-3.29-2.5-4.03v8.05C15.48,15.29,16.5,13.77,16.5,12z"></path></svg>';
              actionElement.appendChild(audioIcon);

              actionElement.appendChild(audio);

              // إزالة العنصر بعد انتهاء الصوت
              audio.onended = function() {
                if (actionContainer.contains(actionElement)) {
                  actionContainer.removeChild(actionElement);
                }
              };
            }

            actionContainer.appendChild(actionElement);

            // تفعيل التأثير - جعل العنصر مرئيًا فورًا
            actionElement.classList.add('active');



            // إزالة العنصر بعد المدة المحددة إذا لم تكن ملف صوتي
            if (foundAction.mediaType !== 'audio') {
              const duration = foundAction.mediaType === 'video' ? 30000 : 6000;
              setTimeout(() => {
                if (actionContainer.contains(actionElement)) {
                  actionElement.classList.remove('active');
                  actionContainer.removeChild(actionElement);
                }
              }, duration);
            }
          }
        });
      }
    }

    // محاكاة ضغط مفتاح
    function simulateKeypress(keypressInfo) {
      if (!keypressInfo) return;

      try {
        console.log('محاكاة ضغط مفتاح:', keypressInfo);

        let key = keypressInfo.key || '';
        const modifiers = keypressInfo.modifiers || {};
        const isCustom = keypressInfo.isCustom || false;

        // تحويل مفاتيح خاصة
        if (!isCustom) {
          switch (key) {
            case 'space':
              key = ' ';
              break;
            case 'enter':
              key = 'Enter';
              break;
            case 'escape':
              key = 'Escape';
              break;
            case 'tab':
              key = 'Tab';
              break;
            case 'arrowup':
              key = 'ArrowUp';
              break;
            case 'arrowdown':
              key = 'ArrowDown';
              break;
            case 'arrowleft':
              key = 'ArrowLeft';
              break;
            case 'arrowright':
              key = 'ArrowRight';
              break;
            case 'backspace':
              key = 'Backspace';
              break;
            // المزيد من المفاتيح الخاصة يمكن إضافتها هنا
          }
        }

        // إنشاء حدث keydown
        const keydownEvent = new KeyboardEvent('keydown', {
          key: key,
          code: getKeyCode(key),
          ctrlKey: modifiers.ctrl || false,
          altKey: modifiers.alt || false,
          shiftKey: modifiers.shift || false,
          bubbles: true
        });

        // إنشاء حدث keyup
        const keyupEvent = new KeyboardEvent('keyup', {
          key: key,
          code: getKeyCode(key),
          ctrlKey: modifiers.ctrl || false,
          altKey: modifiers.alt || false,
          shiftKey: modifiers.shift || false,
          bubbles: true
        });

        // إرسال الأحداث
        document.dispatchEvent(keydownEvent);

        // تأخير قصير بين keydown و keyup
        setTimeout(() => {
          document.dispatchEvent(keyupEvent);
        }, 50);

        console.log('تم محاكاة ضغط المفتاح:', key);
      } catch (error) {
        console.error('خطأ في محاكاة ضغط المفتاح:', error);
      }
    }

    // استخراج كود المفتاح من المفتاح
    function getKeyCode(key) {
      // المفاتيح الخاصة الشائعة
      const keyCodes = {
        ' ': 'Space',
        'Enter': 'Enter',
        'Escape': 'Escape',
        'Tab': 'Tab',
        'ArrowUp': 'ArrowUp',
        'ArrowDown': 'ArrowDown',
        'ArrowLeft': 'ArrowLeft',
        'ArrowRight': 'ArrowRight',
        'Backspace': 'Backspace',
        'Delete': 'Delete',
        'Control': 'ControlLeft',
        'Alt': 'AltLeft',
        'Shift': 'ShiftLeft',
        'CapsLock': 'CapsLock',
        'Home': 'Home',
        'End': 'End',
        'PageUp': 'PageUp',
        'PageDown': 'PageDown'
      };

      // التحقق من وجود المفتاح في القائمة
      if (key in keyCodes) {
        return keyCodes[key];
      }

      // للمفاتيح العادية (حرف أو رقم)
      if (key.length === 1) {
        if (/[a-z]/i.test(key)) {
          return 'Key' + key.toUpperCase();
        } else if (/[0-9]/.test(key)) {
          return 'Digit' + key;
        }
      }

      // إذا كان مفتاح وظيفي (F1-F12)
      if (/^F\d{1,2}$/.test(key)) {
        return key;
      }

      // إذا لم يتم التعرف على المفتاح
      return 'Unidentified';
    }

    // عرض صورة المستخدم مع اسمه
    function showUserDisplay(mapping) {
      try {
        console.log('🔥 بدء عرض صورة المستخدم:', mapping);
        console.log('🔥 إعدادات عرض المستخدم الخام:', mapping.userDisplaySettings);
        console.log('🔥 نوع البيانات:', typeof mapping.userDisplaySettings);

        // إعدادات ثابتة - وسط اليسار وحجم 25px
        const settings = {
          position: 'middle-left',
          size: 25,
          showUsername: mapping.userDisplaySettings?.showUsername !== false
        };

        // إنشاء عنصر عرض صورة المستخدم
        const userDisplayElement = document.createElement('div');
        userDisplayElement.className = 'user-display-item';

        // تطبيق موقع العرض
        userDisplayElement.classList.add(`position-${settings.position}`);

        // إنشاء صورة المستخدم بحجم ثابت 25px مع !important
        const userImage = document.createElement('img');
        userImage.style.cssText = `
          width: 80px !important;
          height: 80px !important;
          min-width: 80px !important;
          min-height: 80px !important;
          max-width: 80px !important;
          max-height: 80px !important;
          border-radius: 50% !important;
          object-fit: cover !important;
          display: block !important;
          box-sizing: border-box !important;
        `;

        // استخدام صورة المستخدم الحقيقية إذا كانت متوفرة، وإلا استخدام الصورة الافتراضية
        if (mapping.profilePictureUrl) {
          userImage.src = mapping.profilePictureUrl;
          console.log('استخدام صورة المستخدم الحقيقية:', mapping.profilePictureUrl);
        } else {
          userImage.src = '/images/default-user.svg';
          console.log('استخدام الصورة الافتراضية');
        }
        userImage.alt = mapping.nickname || 'مستخدم';
        userImage.onerror = function() {
          // إنشاء صورة افتراضية بالأحرف الأولى من الاسم
          const canvas = document.createElement('canvas');
          const size = 25;
          canvas.width = size;
          canvas.height = size;
          const ctx = canvas.getContext('2d');

          // رسم خلفية دائرية
          ctx.fillStyle = '#ff3b5c';
          ctx.beginPath();
          ctx.arc(size/2, size/2, size/2, 0, 2 * Math.PI);
          ctx.fill();

          // رسم الحرف الأول من الاسم
          ctx.fillStyle = 'white';
          ctx.font = `bold ${Math.max(size/3, 12)}px Arial`;
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          const firstLetter = (mapping.nickname || 'U').charAt(0).toUpperCase();
          ctx.fillText(firstLetter, size/2, size/2);

          this.src = canvas.toDataURL();
        };

        userDisplayElement.appendChild(userImage);

        // إضافة اسم المستخدم مع ظل قوي وواضح
        if (settings.showUsername && mapping.nickname) {
          console.log('🔥 إضافة اسم المستخدم:', mapping.nickname);
          const usernameElement = document.createElement('div');
          usernameElement.className = 'username-text';
          usernameElement.textContent = mapping.nickname;
          usernameElement.style.cssText = `
            color: #FFFFFF !important;
            font-size: 18px !important;
            font-weight: 900 !important;
            text-align: center !important;
            text-shadow:
              3px 3px 6px rgba(0, 0, 0, 1),
              -2px -2px 4px rgba(0, 0, 0, 0.8),
              2px -2px 4px rgba(0, 0, 0, 0.8),
              -2px 2px 4px rgba(0, 0, 0, 0.8),
              0px 0px 12px rgba(0, 0, 0, 0.9),
              0px 0px 20px rgba(0, 0, 0, 0.7) !important;
            font-family: 'Tajawal', Arial, sans-serif !important;
            margin-top: 10px !important;
            position: absolute !important;
            top: 100% !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            white-space: nowrap !important;
            background: rgba(0, 0, 0, 0.6) !important;
            padding: 6px 12px !important;
            border-radius: 15px !important;
            backdrop-filter: blur(8px) !important;
            border: 2px solid rgba(255, 255, 255, 0.2) !important;
            z-index: 10001 !important;
          `;
          userDisplayElement.appendChild(usernameElement);
          console.log('🔥 تم إضافة عنصر الاسم');
        } else {
          console.log('🔥 لن يتم عرض الاسم - showUsername:', settings.showUsername, 'nickname:', mapping.nickname);
        }

        // موقع ثابت - وسط اليسار
        const positionStyle = {
          top: '30%',
          left: '220px',
          transform: 'translateY(-50%)'
        };
        Object.assign(userDisplayElement.style, {
          position: 'fixed',
          zIndex: '10000',
          opacity: '0',
          transition: 'all 0.3s ease',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          ...positionStyle
        });

        // إضافة العنصر إلى الصفحة
        document.body.appendChild(userDisplayElement);

        // تفعيل التأثير البصري بدون تكبير
        setTimeout(() => {
          userDisplayElement.style.opacity = '1';
          userDisplayElement.style.transform = positionStyle.transform || '';
        }, 50);



        // استخدام مدة العرض العامة من التعيين (mapping.duration)
        const generalDuration = parseInt(mapping.duration) || 5;
        console.log(`🔥 استخدام مدة العرض العامة: ${generalDuration} ثانية`);

        if (generalDuration > 0) {
          const durationMs = generalDuration * 1000;
          console.log(`🔥 سيتم إزالة العنصر بعد ${durationMs} ميلي ثانية`);

          setTimeout(() => {
            console.log(`🔥 بدء إخفاء العنصر بعد ${generalDuration} ثانية`);
            userDisplayElement.style.opacity = '0';
            userDisplayElement.style.transform = positionStyle.transform || '';

            setTimeout(() => {
              if (userDisplayElement && userDisplayElement.parentNode) {
                userDisplayElement.remove();
                console.log(`🔥 تم إزالة العنصر نهائياً`);
              }
            }, 300);
          }, durationMs);
        } else {
          console.log(`🔥 مدة العرض العامة = 0 - عرض دائم`);
        }

      } catch (error) {
        console.error('خطأ في عرض صورة المستخدم:', error);
      }
    }
  </script>
  <script src="/js/translation.js"></script>
</body>
</html>