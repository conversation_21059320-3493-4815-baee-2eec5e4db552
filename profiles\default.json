{"giftMappings": [{"id": "1750504759691", "mappingName": "kick", "eventType": "gift", "actions": ["sound", "image", "keypress"], "duration": 1, "overlayId": "1", "giftId": "5655", "giftName": "", "giftImage": "", "condition": "any", "keypressSequence": [{"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 0}], "mediaFilePath": "/uploads/1751630713972-Dragon Ball Sticker by Toei Animation.gif", "soundFilePath": "/uploads/1751630708623-da.mp3", "playSoundOnServer": true}, {"id": "1750505308497", "mappingName": "kick 2", "eventType": "gift", "actions": ["sound", "image", "keypress"], "duration": 1, "overlayId": "2", "giftId": "5269", "giftName": "", "giftImage": "", "condition": "any", "keypressSequence": [{"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 0}], "mediaFilePath": "/uploads/1750505307031-Whip It Good Stop Motion Sticker by Mighty Oak.gif", "soundFilePath": "/uploads/1750952328266-crack_the_whip.mp3", "playSoundOnServer": false}, {"id": "1750505595995", "mappingName": "kick 3", "eventType": "gift", "actions": ["sound", "image", "keypress"], "duration": 2, "overlayId": "3", "giftId": "5487", "giftName": "", "giftImage": "", "condition": "any", "keypressSequence": [{"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 0}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}], "mediaFilePath": "/uploads/1750505595401-nghiep.gif", "soundFilePath": "/uploads/1750952347356-nghiep.mp3", "playSoundOnServer": false}, {"id": "1750505954353", "mappingName": "kick 4", "eventType": "gift", "actions": ["sound", "image", "keypress"], "duration": 3, "overlayId": "4", "giftId": "5658", "giftName": "", "giftImage": "", "condition": "any", "keypressSequence": [{"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 0}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}], "mediaFilePath": "/uploads/1750505951265-songoku.gif", "soundFilePath": "/uploads/1750506003388-songoku.mp3", "playSoundOnServer": false}, {"id": "1750506237066", "mappingName": "kick 5", "eventType": "gift", "actions": ["sound", "image", "keypress"], "duration": 5, "overlayId": "5", "giftId": "5879", "giftName": "", "giftImage": "", "condition": "any", "keypressSequence": [{"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 0}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}], "mediaFilePath": "/uploads/1750506235870-metal-slug-soldier.gif", "soundFilePath": "/uploads/1750952302642-sungmay.mp3", "playSoundOnServer": false}, {"id": "1750507459588", "mappingName": "help", "eventType": "gift", "actions": ["sound", "image", "keypress"], "duration": 20, "overlayId": "6", "giftId": "5659", "giftName": "", "giftImage": "", "condition": "any", "keypressSequence": [{"key": "space", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": true, "holdDuration": 20000, "isCustom": false, "delay": 0}], "mediaFilePath": "/uploads/1750507458770-Angel Wings Sticker by Phat Kandi.gif", "soundFilePath": "/uploads/1750952253137-fly-me-to-the-moon.mp3", "playSoundOnServer": false}, {"id": "1750515030420", "mappingName": "help 2", "eventType": "gift", "actions": ["sound", "image", "keypress"], "duration": 40, "overlayId": "7", "giftId": "6267", "giftName": "", "giftImage": "", "condition": "any", "keypressSequence": [{"key": "space", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": true, "holdDuration": 40000, "isCustom": false, "delay": 0}], "mediaFilePath": "/uploads/1750515061742-Angel Wings Baby Sticker by Nuby USA.gif", "soundFilePath": "/uploads/1750952190620-tom-jones-its-not-unusual-11.mp3", "playSoundOnServer": false}, {"id": "1750517783570", "mappingName": "<PERSON><PERSON>", "eventType": "gift", "actions": ["sound", "image", "keypress"], "duration": 44, "overlayId": "8", "giftId": "6820", "giftName": "", "giftImage": "", "condition": "any", "keypressSequence": [{"key": "k", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 0}], "mediaFilePath": "/uploads/1750517782726-3D Skull Sticker by badblueprints.gif", "soundFilePath": "/uploads/1750952176305-wide putin walking but its fnaf full version  fnaf-[AudioTrimmer (mp3cut.net).mp3", "playSoundOnServer": false}, {"id": "1750518346921", "mappingName": "end game", "eventType": "gift", "actions": ["sound", "image"], "duration": 51, "overlayId": "9", "giftId": "6369", "giftName": "", "giftImage": "", "condition": "any", "mediaFilePath": "/uploads/1750518345907-3D Skull Sticker by badblueprints (1).gif", "soundFilePath": "/uploads/1750518474403-causmic-flight-to-tunisia-[AudioTrimmer.com](1).mp3", "playSoundOnServer": false}], "displaySettings": {"showUsernames": true, "showGiftNames": true, "showGiftImages": true, "showDiamondCount": true, "alertPosition": "center", "alertTheme": "default", "alertSize": "normal", "alertDuration": 5, "showBigGifts": true, "showFollows": false, "showLikes": false, "showComments": false, "backgroundColor": "#000000", "textColor": "#ffffff", "accentColor": "#ff3b5c", "fontFamily": "'<PERSON><PERSON><PERSON>', sans-serif", "fontSize": 16, "enableAnimations": false, "animationType": "shake", "width": 30, "opacity": 80, "backgroundOpacity": 0, "maxItems": 5, "customPosition": {"x": 10, "y": 10}, "sound": {"enabled": false, "volume": 50}}, "ttsSettings": {"enabled": true, "voice": "ar-SA-HamedNeural", "speed": 1, "volume": 100, "minLength": 1, "maxLength": 500, "readRate": 2, "blockedWords": [], "models": [{"id": "ar-EG-SalmaNeural", "name": "العربية (مصر) - سلمى (أنثى)"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "العربية (مصر) - <PERSON><PERSON><PERSON><PERSON> (ذكر)"}, {"id": "ar-SA-HamedNeural", "name": "العربية (السعودية) - حا<PERSON><PERSON> (ذكر)"}, {"id": "ar-SA-ZariyahNeural", "name": "العربية (السعودية) - زارية (أنثى)"}, {"id": "ar-AE-FatimaNeural", "name": "العربية (الإمارات) - فاطمة (أنثى)"}, {"id": "ar-AE-HamdanNeural", "name": "العربية (الإمارات) - ح<PERSON><PERSON><PERSON> (ذكر)"}, {"id": "ar-DZ-AminaNeural", "name": "العربية (الجزائر) - أمينة (أنثى)"}, {"id": "ar-DZ-Ismael<PERSON>eural", "name": "العربية (الجزائر) - إس<PERSON><PERSON><PERSON><PERSON><PERSON> (ذكر)"}, {"id": "ar-BH-AliNeural", "name": "العربية (البحرين) - علي (ذكر)"}, {"id": "ar-BH-<PERSON><PERSON>eural", "name": "العربية (البحرين) - ليلى (أنثى)"}, {"id": "ar-IQ-BasselNeural", "name": "العربية (العراق) - با<PERSON><PERSON> (ذكر)"}, {"id": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "العربية (العراق) - رنا (أنثى)"}, {"id": "ar-JO-SanaNeural", "name": "العربية (الأردن) - سناء (أنثى)"}, {"id": "ar-JO-TaimNeural", "name": "العربية (الأردن) - تيم (ذكر)"}, {"id": "ar-KW-FahedNeural", "name": "العربية (الكويت) - فهد (ذكر)"}, {"id": "ar-KW-NouraNeural", "name": "العربية (الكويت) - نورة (أنثى)"}, {"id": "ar-LB-<PERSON><PERSON><PERSON><PERSON>", "name": "العربية (لبنان) - ليلى (أنثى)"}, {"id": "ar-LB-RamiNeural", "name": "العربية (لبنان) - را<PERSON><PERSON> (ذكر)"}, {"id": "ar-LY-ImanNeural", "name": "العربية (ليبيا) - <PERSON><PERSON><PERSON><PERSON> (أن<PERSON>ى)"}, {"id": "ar-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "name": "العربية (ليبيا) - <PERSON><PERSON><PERSON> (ذ<PERSON><PERSON>)"}, {"id": "ar-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "العربية (المغرب) - ج<PERSON><PERSON><PERSON> (ذكر)"}, {"id": "ar-<PERSON><PERSON><PERSON><PERSON>N<PERSON><PERSON>", "name": "العربية (المغرب) - من<PERSON> (أنثى)"}, {"id": "ar-O<PERSON><PERSON>Abdullah<PERSON><PERSON><PERSON>", "name": "العربية (عمان) - ع<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ذكر)"}, {"id": "ar-OM-AyshaNeural", "name": "العربية (عمان) - عائشة (أنثى)"}, {"id": "ar-QA-AmalNeural", "name": "العربية (قطر) - <PERSON><PERSON><PERSON> (أنثى)"}, {"id": "ar-QA-MoazNeural", "name": "العربية (قطر) - معاذ (ذكر)"}, {"id": "ar-SY-AmanyNeural", "name": "العربية (سوريا) - أم<PERSON>ي (أنثى)"}, {"id": "ar-S<PERSON>-<PERSON>thNeural", "name": "العربية (سوريا) - ليث (ذكر)"}, {"id": "ar-TN-HediNeural", "name": "العربية (تونس) - هادي (ذكر)"}, {"id": "ar-TN-ReemNeural", "name": "العربية (تونس) - ريم (أنثى)"}, {"id": "ar-<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "العربية (اليمن) - مريم (أنثى)"}, {"id": "ar-YE-SalehNeural", "name": "العربية (اليمن) - صال<PERSON> (ذكر)"}, {"id": "en-US-AriaNeural", "name": "الإنجليزية (أمريكية) - آريا (أنثى)"}, {"id": "en-US-GuyN<PERSON><PERSON>", "name": "الإنجليزية (أمريكية) - جاي (ذكر)"}, {"id": "en-US-<PERSON><PERSON><PERSON><PERSON>", "name": "الإنجليزية (أمريكية) - جيني (أنثى)"}, {"id": "en-GB-<PERSON><PERSON><PERSON><PERSON>", "name": "الإنجليزية (بريطانية) - سونيا (أنثى)"}, {"id": "en-GB-RyanN<PERSON><PERSON>", "name": "الإنجليزية (بريطانية) - <PERSON><PERSON><PERSON><PERSON> (ذكر)"}, {"id": "fr-FR-<PERSON><PERSON>", "name": "الفرنسية - دينيز (أنثى)"}, {"id": "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "name": "الفرنسية - <PERSON><PERSON><PERSON><PERSON> (ذك<PERSON>)"}, {"id": "de-DE-Katja<PERSON>eural", "name": "الألمانية - كاتيا (أنثى)"}, {"id": "de-DE-ConradNeural", "name": "الألمانية - كونر<PERSON> (ذكر)"}, {"id": "es-ES-Elvira<PERSON>eural", "name": "الإسبانية - إلفيرا (أنثى)"}, {"id": "es-ES-AlvaroNeural", "name": "الإسبانية - أ<PERSON><PERSON><PERSON><PERSON><PERSON> (ذكر)"}]}}